<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        /* Essential custom styles only */
        .annotation-comment {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.875rem;
            max-width: 250px;
            word-wrap: break-word;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 2px solid #007bff;
            pointer-events: none;
        }

        .annotation-comment::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid rgba(0, 0, 0, 0.9);
        }

        .image-container {
            position: relative;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Instructions
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <div class="text-muted">
                            <h6 class="mb-3"><i class="fas fa-mouse-pointer me-2"></i>How to use:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-plus-circle text-success me-2"></i>Click and drag to create annotations</li>
                                <li class="mb-2"><i class="fas fa-comment text-primary me-2"></i>Comments can be empty or filled</li>
                                <li class="mb-2"><i class="fas fa-eye text-info me-2"></i>Comments appear directly under annotations</li>
                                <li class="mb-2"><i class="fas fa-edit text-warning me-2"></i>Click annotations to edit them</li>
                            </ul>
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>New:</strong> Comments are always visible under annotations. Empty comments show "No comment".
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Annotations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
        }
        console.log(JSON.parse(localStorage.getItem('annotorious-comments')));

        function saveAnnotationToStorage(annotation) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            annotations.push(annotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }





        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }





        // Function to add persistent comment display under annotation
        function addAnnotationComment(annotation) {
            const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || '';

            console.log('Adding comment for annotation:', annotation.id, 'Text:', commentText);

            // Create a comment element
            const commentId = `comment-${annotation.id}`;

            // Remove existing comment if any
            const existingComment = document.getElementById(commentId);
            if (existingComment) {
                existingComment.remove();
            }

            // Get annotation bounds and image element
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');

            console.log('Annotation bounds:', bounds);

            // Get image dimensions and position
            const imageRect = imageElement.getBoundingClientRect();
            const imageContainer = imageElement.parentElement;
            const containerRect = imageContainer.getBoundingClientRect();

            // Calculate position relative to the container
            const scaleX = imageRect.width / imageElement.naturalWidth;
            const scaleY = imageRect.height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10; // 10px below the annotation

            console.log('Comment position:', commentX, commentY);
            console.log('Image scale:', scaleX, scaleY);

            // Create comment element
            const commentElement = document.createElement('div');
            commentElement.id = commentId;
            commentElement.className = 'annotation-comment';
            commentElement.style.cssText = `
                left: ${commentX}px;
                top: ${commentY}px;
                transform: translateX(-50%);
                display: block;
            `;

            // Display comment text or placeholder for empty comments
            commentElement.textContent = commentText.trim() || 'No comment';

            console.log('Created comment element:', commentElement);

            // Add to image container
            imageContainer.classList.add('image-container');
            imageContainer.appendChild(commentElement);

            console.log('Comment added to container');
        }

        // Function to remove annotation comment
        function removeAnnotationComment(annotationId) {
            const commentId = `comment-${annotationId}`;
            const commentElement = document.getElementById(commentId);
            if (commentElement) {
                commentElement.remove();
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all annotations? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                localStorage.removeItem('annotorious-annotations');

                // Clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }

                // Remove all comment elements from the page
                const commentElements = document.querySelectorAll('.annotation-comment');
                commentElements.forEach(element => {
                    element.remove();
                });
            }
        }



        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);

                    // Add comment display for restored annotation
                    setTimeout(() => {
                        addAnnotationComment(annotation);
                    }, 500); // Small delay to ensure annotation is rendered

                    console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // jQuery document ready function
        $(document).ready(function () {

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Extract comment text
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || '';

                // Save the annotation to localStorage
                saveAnnotationToStorage(annotation);

                // Extract comment data and save to localStorage
                const commentData = {
                    text: commentText,
                    user: 'Anonymous User',
                    annotation: annotation,
                    annotationId: annotation.id
                };

                saveCommentToStorage(commentData);

                // Add comment display under the annotation
                addAnnotationComment(annotation);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                }

                // Update the comment display
                addAnnotationComment(annotation);
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Remove the comment display
                removeAnnotationComment(annotation.id);
            });

            // Event: When annotation selection changes
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);
                // Comments are always visible, no need to hide/show
            });

            // Event: When annotation is clicked
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);
                // Comments are always visible
            });

            // Event: When mouse enters annotation
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);
                // Comments are always visible
            });

            // Event: When mouse leaves annotation
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);
                // Comments are always visible
            });
        });
    </script>
</body>

</html>