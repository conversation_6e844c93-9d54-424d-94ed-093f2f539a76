<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        /* Essential custom styles only */
        .comment-highlighted {
            border: 2px solid #0d6efd !important;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
        }

        .comment-edit-input {
            resize: vertical;
            min-height: 60px;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Comments
                            <span class="badge bg-light text-dark ms-2" id="comment-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-2" style="max-height: 600px; overflow-y: auto;">
                        <div id="comments-list"></div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Comments
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
            displayComments();
        }
        console.log(JSON.parse(localStorage.getItem('annotorious-comments')));

        function saveAnnotationToStorage(annotation) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            annotations.push(annotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }



        // jQuery function to highlight comment
        function highlightComment(annotationId) {
            // Remove previous highlights
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');

            // Find and highlight the comment
            const $commentItem = $(`.comment-item[data-annotation-id="${annotationId}"]`);
            if ($commentItem.length > 0) {
                $commentItem.addClass('comment-highlighted highlight-pulse');

                // Scroll to the comment if it's not visible
                $commentItem[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                // setTimeout(() => {
                //     $commentItem.removeClass('comment-highlighted highlight-pulse');
                // }, 3000);
            }
        }

        // jQuery function to remove all highlights
        function removeAllHighlights() {
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');
        }

        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }

        function displayComments() {
            const commentsList = document.getElementById('comments-list');
            let comments = loadCommentsFromStorage();

            // Update comment count
            updateCommentCount(comments.length);

            if (comments.length === 0) {
                commentsList.innerHTML = `
                    <div class="text-center p-4 text-muted">
                        <i class="fas fa-comments display-4 mb-3 opacity-50"></i>
                        <h6 class="mb-2">No Comments Yet</h6>
                        <p class="mb-0 small">Create an annotation to add your first comment!</p>
                    </div>
                `;
                return;
            }

            commentsList.innerHTML = comments.map(comment => {
                const userName = comment.user || 'Anonymous';
                const userInitial = userName.charAt(0).toUpperCase();
                const timeAgo = getTimeAgo(comment.timestamp);

                return `
                    <div class="card mb-3 comment-item" data-comment-id="${comment.id}" data-annotation-id="${comment.annotationId}">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white fw-bold me-3" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                    ${userInitial}
                                </div>
                                <div class="flex-grow-1">
                                    <p class="mb-0 fw-semibold">${userName}</p>
                                    <p class="mb-0 text-muted small">
                                        <i class="far fa-clock me-1"></i>${timeAgo}
                                    </p>
                                </div>
                            </div>

                            <div class="comment-content">
                                <div id="comment-text-${comment.id}" class="mb-3">
                                    <i class="fas fa-quote-left text-muted me-2"></i>
                                    ${comment.text || 'No comment text'}
                                </div>
                                <div id="edit-container-${comment.id}" style="display: none;">
                                    <textarea class="form-control comment-edit-input mb-2" id="edit-input-${comment.id}" placeholder="Edit your comment...">${comment.text || ''}</textarea>
                                </div>
                            </div>

                            <div class="d-flex gap-1">
                                <button class="btn btn-outline-primary btn-sm" onclick="editComment('${comment.id}')">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteComment('${comment.id}', '${comment.annotationId}')">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </button>
                                <button class="btn btn-success btn-sm" id="save-btn-${comment.id}" style="display: none;" onclick="saveComment('${comment.id}', '${comment.annotationId}')">
                                    <i class="fas fa-save me-1"></i>Save
                                </button>
                                <button class="btn btn-secondary btn-sm" id="cancel-btn-${comment.id}" style="display: none;" onclick="cancelEdit('${comment.id}')">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Helper function to calculate time ago
        function getTimeAgo(timestamp) {
            if (!timestamp) return 'Unknown time';

            const now = new Date();
            const commentTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - commentTime) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return commentTime.toLocaleDateString();
        }

        // Helper function to update comment count
        function updateCommentCount(count) {
            const countBadge = document.getElementById('comment-count');
            if (countBadge) {
                countBadge.textContent = count;
                countBadge.className = count > 0 ? 'badge bg-primary ms-2' : 'badge bg-light text-dark ms-2';
            }
        }

        // Function to add message display under annotation
        function addAnnotationMessage(annotation) {
            const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || '';
            if (!commentText.trim()) return;

            // Create a message element
            const messageId = `annotation-message-${annotation.id}`;

            // Remove existing message if any
            const existingMessage = document.getElementById(messageId);
            if (existingMessage) {
                existingMessage.remove();
            }

            // Get annotation bounds
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');
            const imageRect = imageElement.getBoundingClientRect();

            // Calculate position relative to the image
            const messageX = bounds.minX + (bounds.maxX - bounds.minX) / 2;
            const messageY = bounds.maxY + 10; // 10px below the annotation

            // Create message element
            const messageElement = document.createElement('div');
            messageElement.id = messageId;
            messageElement.className = 'annotation-message position-absolute bg-dark text-white px-2 py-1 rounded small';
            messageElement.style.cssText = `
                left: ${messageX}px;
                top: ${messageY}px;
                transform: translateX(-50%);
                z-index: 1000;
                max-width: 200px;
                word-wrap: break-word;
                pointer-events: none;
            `;
            messageElement.textContent = commentText;

            // Add to image container
            const imageContainer = imageElement.parentElement;
            imageContainer.style.position = 'relative';
            imageContainer.appendChild(messageElement);

            // Auto-hide after 3 seconds
            setTimeout(() => {
                if (document.getElementById(messageId)) {
                    messageElement.remove();
                }
            }, 3000);
        }

        // Function to remove annotation message
        function removeAnnotationMessage(annotationId) {
            const messageId = `annotation-message-${annotationId}`;
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        function editComment(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.hide();
            $editContainer.show();
            $editBtn.hide();
            $deleteBtn.hide();
            $saveBtn.show();
            $cancelBtn.show();
        }

        function cancelEdit(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.show();
            $editContainer.hide();
            $editBtn.show();
            $deleteBtn.show();
            $saveBtn.hide();
            $cancelBtn.hide();
        }

        function saveComment(commentId, annotationId) {
            const newText = $(`#edit-input-${commentId}`).val().trim();

            if (!newText) {
                alert('Comment cannot be empty!');
                return;
            }

            // Update comment in localStorage
            let comments = loadCommentsFromStorage();
            const commentIndex = comments.findIndex(c => c.id === commentId);

            if (commentIndex !== -1) {
                comments[commentIndex].text = newText;
                comments[commentIndex].timestamp = new Date().toLocaleString();
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Update the annotation in Annotorious
                updateAnnotationComment(annotationId, newText);

                displayComments();
            }
        }

        function deleteComment(commentId, annotationId) {
            if (confirm('Are you sure you want to delete this comment?')) {
                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.id !== commentId);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Delete the annotation from Annotorious
                deleteAnnotationFromMap(annotationId);

                displayComments();
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all comments? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                localStorage.removeItem('annotorious-annotations');
                // Also clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }
                displayComments();
            }
        }

        // Functions to interact with Annotorious annotations
        function updateAnnotationComment(annotationId, newText) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // Create updated annotation with new comment
                const updatedAnnotation = {
                    ...annotation,
                    bodies: annotation.bodies.map(body => {
                        if (body.purpose === 'commenting') {
                            return {
                                ...body,
                                value: newText,
                                modified: new Date().toISOString()
                            };
                        }
                        return body;
                    })
                };

                // Update the annotation in Annotorious
                window.annotoriusInstance.updateAnnotation(updatedAnnotation, annotation);
            }
        }

        function deleteAnnotationFromMap(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                window.annotoriusInstance.removeAnnotation(annotation);
                removeAnnotationFromStorage(annotationId);
                removeAnnotationMessage(annotationId);
            }
        }

        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);

                    // Add message display for restored annotation
                    setTimeout(() => {
                        addAnnotationMessage(annotation);
                    }, 500); // Small delay to ensure annotation is rendered

                    console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // jQuery document ready function
        $(document).ready(function () {
            // Initialize comments display
            displayComments();

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Extract comment text
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || '';

                // Check if comment is empty or just whitespace
                if (!commentText.trim()) {
                    // Remove the annotation if comment is empty
                    anno.removeAnnotation(annotation);
                    alert('Please add a comment to create an annotation.');
                    return;
                }

                // Save the annotation to localStorage
                saveAnnotationToStorage(annotation);

                // Extract comment data and save to localStorage
                const commentData = {
                    text: commentText,
                    user: 'Anonymous User',
                    annotation: annotation,
                    annotationId: annotation.id
                };

                saveCommentToStorage(commentData);

                // Add message display under the annotation
                addAnnotationMessage(annotation);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                    displayComments();
                }
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                displayComments();
            });

            // Event: When annotation selection changes - HIGHLIGHT FEATURE
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Remove all previous highlights
                removeAllHighlights();

                // Highlight comments for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        highlightComment(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked - HIGHLIGHT FEATURE
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);

                // Highlight the corresponding comment
                highlightComment(annotation.id);
            });

            // Event: When mouse enters annotation - SHOW MESSAGE
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);

                // Show annotation message
                addAnnotationMessage(annotation);

                // Add subtle highlight to corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.addClass('highlight-pulse');
            });

            // Event: When mouse leaves annotation - HIDE MESSAGE
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);

                // Hide annotation message after a delay
                setTimeout(() => {
                    removeAnnotationMessage(annotation.id);
                }, 1000);

                // Remove subtle highlight from corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.removeClass('highlight-pulse');
            });

            // Add click event to comments to select and highlight corresponding annotation
            $(document).on('click', '.comment-item', function () {
                const annotationId = $(this).data('annotation-id');
                if (annotationId && window.annotoriusInstance) {
                    // Remove all previous highlights
                    removeAllHighlights();

                    // Highlight the annotation on the image
                    highlightAnnotationOnImage(annotationId);

                    // Highlight this comment
                    highlightComment(annotationId);
                }
            });
        });
    </script>
</body>

</html>